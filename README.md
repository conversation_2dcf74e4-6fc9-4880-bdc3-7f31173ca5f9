# RSI Accumulation Strategy Expert Advisor

A MetaTrader 4 Expert Advisor (EA) that implements an RSI-based accumulation trading strategy with configurable parameters and optional Moving Average filter.

## Overview

This Expert Advisor uses the Relative Strength Index (RSI) indicator to identify potential trading opportunities based on accumulated RSI values. The strategy enters trades when the accumulated RSI exceeds a configurable threshold and can optionally use a Moving Average filter for trade confirmation.

## Features

- **RSI-Based Trading Logic**: Uses RSI values with configurable periods and overbought/oversold levels
- **Accumulation Strategy**: Trades based on accumulated RSI values exceeding a trigger threshold
- **Moving Average Filter**: Optional MA filter for trade confirmation with adjustable parameters
- **Flexible Exit Strategy**: RSI-based take profit and stop loss levels for both buy and sell positions
- **Risk Management**: Configurable lot size, magic number, and slippage settings

## Configuration Parameters

### RSI Settings
- `RSI_Period`: Period for RSI calculation
- `RSI_Overbought`: Overbought level threshold
- `RSI_Oversold`: Oversold level threshold
- `TriggerThreshold`: Threshold for accumulated RSI values to trigger trades

### Moving Average Filter (Optional)
- `MA_Period`: Period for Moving Average calculation
- `MA_Method`: Moving Average method (SMA, EMA, SMMA, LWMA)
- `MA_Applied`: Price type for MA calculation (Close, Open, High, Low, etc.)

### Exit Strategy
- `Buy_TakeProfit_RSI`: RSI level for take profit on buy positions
- `Sell_TakeProfit_RSI`: RSI level for take profit on sell positions
- `Buy_StopLoss_RSI`: RSI level for stop loss on buy positions
- `Sell_StopLoss_RSI`: RSI level for stop loss on sell positions

### Trade Management
- `LotSize`: Position size for trades
- `MagicNumber`: Unique identifier for EA trades
- `Slippage`: Maximum allowed slippage in points

## Installation

1. Copy the `rsi.ex4` file to your MetaTrader 4 `Experts` folder
2. Restart MetaTrader 4 or refresh the Navigator panel
3. Drag the EA onto your desired chart
4. Configure the parameters according to your trading preferences
5. Enable automated trading in MetaTrader 4

## Usage

1. **Attach to Chart**: Drag the EA from the Navigator to your preferred currency pair chart
2. **Configure Parameters**: Adjust the RSI, MA, and trade management parameters
3. **Enable AutoTrading**: Make sure automated trading is enabled in MT4
4. **Monitor Performance**: Watch the EA's performance and adjust parameters as needed

## Risk Warning

⚠️ **Important**: This Expert Advisor is for educational and research purposes. Trading forex and CFDs involves significant risk and may not be suitable for all investors. Past performance does not guarantee future results. Always test strategies on a demo account before using real money.

## Requirements

- MetaTrader 4 platform
- Sufficient account balance for the configured lot size
- Stable internet connection for automated trading

## Support

For questions or issues related to this EA, please refer to the MetaTrader 4 documentation or consult with a qualified trading advisor.

## License

This project is provided as-is for educational purposes. Use at your own risk.